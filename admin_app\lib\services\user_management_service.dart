import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';

class UserManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all users with pagination
  static Future<List<UserModel>> getUsers({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    String? searchQuery,
    UserRole? roleFilter,
    bool? isActiveFilter,
    bool? isVerifiedFilter,
  }) async {
    try {
      Query query = _firestore.collection(AppConstants.usersCollection);

      // Apply filters
      if (roleFilter != null) {
        query = query.where('role', isEqualTo: roleFilter.value);
      }
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }
      if (isVerifiedFilter != null) {
        query = query.where('isVerified', isEqualTo: isVerifiedFilter);
      }

      // Apply search
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query
            .where('username', isGreaterThanOrEqualTo: searchQuery.toLowerCase())
            .where('username', isLessThanOrEqualTo: '${searchQuery.toLowerCase()}\uf8ff');
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get users: ${e.toString()}');
    }
  }

  // Get user by ID
  static Future<UserModel?> getUserById(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  // Get user's posts
  static Future<List<PostModel>> getUserPosts(String userId, {int limit = 10}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.postsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user posts: ${e.toString()}');
    }
  }

  // Get user products
  static Future<List<ProductModel>> getUserProducts(String userId, {int limit = 50}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection('products')
          .where('sellerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user products: ${e.toString()}');
    }
  }

  // Get user statistics
  static Future<Map<String, int>> getUserStatistics(String userId) async {
    try {
      final futures = await Future.wait([
        _firestore.collection('posts').where('userId', isEqualTo: userId).get(),
        _firestore.collection('products').where('sellerId', isEqualTo: userId).get(),
        _firestore.collection(AppConstants.usersCollection).doc(userId).get(),
      ]);

      final postsSnapshot = futures[0] as QuerySnapshot;
      final productsSnapshot = futures[1] as QuerySnapshot;
      final userSnapshot = futures[2] as DocumentSnapshot;

      int followerCount = 0;
      int followingCount = 0;

      if (userSnapshot.exists) {
        final userData = userSnapshot.data() as Map<String, dynamic>;
        followerCount = userData['followerCount'] ?? 0;
        followingCount = userData['followingCount'] ?? 0;
      }

      return {
        'posts': postsSnapshot.docs.length,
        'products': productsSnapshot.docs.length,
        'followers': followerCount,
        'following': followingCount,
      };
    } catch (e) {
      throw Exception('Failed to get user statistics: ${e.toString()}');
    }
  }

  // Toggle user active status
  static Future<void> toggleUserActiveStatus(String userId, bool isActive) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to toggle user active status: ${e.toString()}');
    }
  }

  // Toggle user verified status
  static Future<void> toggleUserVerifiedStatus(String userId, bool isVerified) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isVerified': isVerified,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to toggle user verified status: ${e.toString()}');
    }
  }

  // Update user role
  static Future<void> updateUserRole(String userId, UserRole newRole) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'role': newRole.value,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update user role: ${e.toString()}');
    }
  }

  // Delete user (soft delete by deactivating)
  static Future<void> deleteUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete user: ${e.toString()}');
    }
  }

  // Search users
  static Future<List<UserModel>> searchUsers(String searchQuery, {int limit = 20}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('username', isGreaterThanOrEqualTo: searchQuery.toLowerCase())
          .where('username', isLessThanOrEqualTo: '${searchQuery.toLowerCase()}\uf8ff')
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  // Get pending reseller applications
  static Future<List<UserModel>> getPendingResellerApplications() async {
    try {
      print('DEBUG: Querying pending reseller applications from Firestore...');
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: 'user')
          .where('resellerApplicationStatus', isEqualTo: 'pending')
          .orderBy('resellerApplicationDate', descending: true)
          .get();

      final applications = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      print('DEBUG: Found ${applications.length} pending reseller applications');
      for (final app in applications) {
        print('DEBUG: - User: ${app.displayNameOrUsername} (${app.email}) applied on ${app.resellerApplicationDate}');
      }

      return applications;
    } catch (e) {
      print('DEBUG ERROR: Failed to get pending reseller applications - $e');
      throw Exception('Failed to get pending reseller applications: ${e.toString()}');
    }
  }

  // Approve reseller application
  static Future<void> approveResellerApplication(String userId) async {
    try {
      print('DEBUG: Approving reseller application for user ID: $userId');
      await _firestore.collection(AppConstants.usersCollection).doc(userId).update({
        'role': 'reseller',
        'resellerApplicationStatus': 'approved',
        'resellerApprovedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('DEBUG: Successfully approved reseller application for user ID: $userId');
    } catch (e) {
      print('DEBUG ERROR: Failed to approve reseller application for user ID: $userId - $e');
      throw Exception('Failed to approve reseller application: ${e.toString()}');
    }
  }

  // Reject reseller application
  static Future<void> rejectResellerApplication(String userId, String reason) async {
    try {
      await _firestore.collection(AppConstants.usersCollection).doc(userId).update({
        'resellerApplicationStatus': 'rejected',
        'resellerRejectionReason': reason,
        'resellerRejectedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to reject reseller application: ${e.toString()}');
    }
  }

  // Send password reset email
  static Future<void> sendPasswordResetEmail(String email) async {
    try {
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }

  // Get users with pending registration
  static Future<List<UserModel>> getPendingRegistrationUsers() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('registrationStatus', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();

      final users = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      return users;
    } catch (e) {
      throw Exception('Failed to get pending registration users: ${e.toString()}');
    }
  }

  // Approve user registration
  static Future<void> approveUserRegistration(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'registrationStatus': 'approved',
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to approve user registration: ${e.toString()}');
    }
  }

  // Reject user registration
  static Future<void> rejectUserRegistration(String userId, String reason) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'registrationStatus': 'rejected',
        'registrationRejectionReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to reject user registration: ${e.toString()}');
    }
  }

  // Get registration statistics
  static Future<Map<String, int>> getRegistrationStatistics() async {
    try {
      final pendingQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('registrationStatus', isEqualTo: 'pending')
          .get();

      final approvedQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('registrationStatus', isEqualTo: 'approved')
          .get();

      final rejectedQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('registrationStatus', isEqualTo: 'rejected')
          .get();

      final statistics = {
        'pending': pendingQuery.docs.length,
        'approved': approvedQuery.docs.length,
        'rejected': rejectedQuery.docs.length,
      };

      return statistics;
    } catch (e) {
      throw Exception('Failed to get registration statistics: ${e.toString()}');
    }
  }

  // Fix existing users registration status (migration function)
  static Future<void> fixExistingUsersRegistrationStatus() async {
    try {
      final QuerySnapshot allUsersQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .get();

      int updatedCount = 0;

      for (var doc in allUsersQuery.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // If registrationStatus field is missing or null
        if (!data.containsKey('registrationStatus') || data['registrationStatus'] == null) {
          await _firestore
              .collection(AppConstants.usersCollection)
              .doc(doc.id)
              .update({
            'registrationStatus': 'approved', // Existing users are considered approved
            'updatedAt': FieldValue.serverTimestamp(),
          });

          updatedCount++;
        }
      }
    } catch (e) {
      throw Exception('Failed to fix existing users registration status: ${e.toString()}');
    }
  }
}
