import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

class SettingsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _settingsCollection = 'app_settings';
  static const String _settingsDocId = 'global_settings';

  // Get approval settings
  static Future<Map<String, bool>> getApprovalSettings() async {
    try {
      final doc = await _firestore
          .collection(_settingsCollection)
          .doc(_settingsDocId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'require_user_approval': data['require_user_approval'] ?? true,
          'require_reseller_approval': data['require_reseller_approval'] ?? true,
        };
      } else {
        // Return default settings if document doesn't exist
        return {
          'require_user_approval': true,
          'require_reseller_approval': true,
        };
      }
    } catch (e) {
      print('Error getting approval settings: $e');
      // Return default settings on error
      return {
        'require_user_approval': true,
        'require_reseller_approval': true,
      };
    }
  }

  // Check if user approval is required
  static Future<bool> isUserApprovalRequired() async {
    try {
      final settings = await getApprovalSettings();
      return settings['require_user_approval'] ?? true;
    } catch (e) {
      print('Error checking user approval requirement: $e');
      return true; // Default to requiring approval on error
    }
  }

  // Check if reseller approval is required
  static Future<bool> isResellerApprovalRequired() async {
    try {
      final settings = await getApprovalSettings();
      return settings['require_reseller_approval'] ?? true;
    } catch (e) {
      print('Error checking reseller approval requirement: $e');
      return true; // Default to requiring approval on error
    }
  }

  // Listen to approval settings changes
  static Stream<Map<String, bool>> watchApprovalSettings() {
    return _firestore
        .collection(_settingsCollection)
        .doc(_settingsDocId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'require_user_approval': data['require_user_approval'] ?? true,
          'require_reseller_approval': data['require_reseller_approval'] ?? true,
        };
      } else {
        return {
          'require_user_approval': true,
          'require_reseller_approval': true,
        };
      }
    });
  }

  // Contact Info Management
  static Future<Map<String, dynamic>> getContactInfo() async {
    try {
      final doc = await _firestore.collection('settings').doc('contact_info').get();
      if (doc.exists) {
        return doc.data() ?? {};
      }
      return {};
    } catch (e) {
      print('Error getting contact info: $e');
      return {};
    }
  }

  static Future<String?> getGlobalWhatsApp() async {
    try {
      final contactInfo = await getContactInfo();
      return contactInfo['whatsApp'];
    } catch (e) {
      print('Error getting global WhatsApp: $e');
      return null;
    }
  }

  static Future<String?> getGlobalWeChat() async {
    try {
      final contactInfo = await getContactInfo();
      return contactInfo['weChat'];
    } catch (e) {
      print('Error getting global WeChat: $e');
      return null;
    }
  }
}
